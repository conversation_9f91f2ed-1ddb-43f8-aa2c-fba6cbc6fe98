#!/usr/bin/env python3
"""
参考任务决策执行Agent

融合决策和执行的Agent，直接调用vision模型输出坐标并执行Android操作
在prompt中包含参考任务的历史和图片
"""

import json
import re
import time
from datetime import datetime
from typing import Dict, Any, Tuple

from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import PydanticOutputParser
from loguru import logger
from pydantic import BaseModel, Field

from src.domain.reference_task.prompt.reference_decision_prompt import build_reference_decision_prompt
from src.domain.reference_task.repo.reference_state import ReferenceTaskState
from src.domain.ui_task.mobile.android.action_tool import (
    execute_simple_action
)
from src.domain.ui_task.mobile.android.screenshot_manager import convert_screenshot_to_base64
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler
from src.infra.model import get_chat_model


class ReferenceDecisionResponse(BaseModel):
    """参考任务决策Agent响应的数据模型"""
    self_check: str = Field(default="", description="自检流程的自检结果")
    interface_analysis: str = Field(default="", description="当前界面分析")
    reference_analysis: str = Field(default="", description="参考任务分析")
    current_step_name: str = Field(default="", description="当前正在执行的步骤名称")
    action_decision: str = Field(default="", description="动作决策结果")
    instruction: str = Field(default="", description="操作指令")
    action: str = Field(default="", description="具体动作命令")


class ReferenceDecisionAgent:
    """参考任务决策执行Agent - 融合决策和执行"""

    def __init__(self):
        """初始化决策执行Agent"""
        self.model = get_chat_model(model_name="copy")

        # 创建JSON解析器
        self.pydantic_parser = PydanticOutputParser(pydantic_object=ReferenceDecisionResponse)

        # 尝试使用专门的修复模型，如果不存在则使用默认模型
        self.output_parser = OutputFixingParser.from_llm(
            parser=self.pydantic_parser,
            llm=get_chat_model(model_name="fix")
        )

    def analyze_decide_and_execute(
            self,
            state: ReferenceTaskState,
            current_screenshot_path: str
    ) -> Dict[str, Any]:
        """
        基于参考任务分析当前界面、决策并执行动作

        Args:
            state: 参考任务状态
            current_screenshot_path: 当前截图路径

        Returns:
            执行结果
        """
        task_id = state["task_id"]
        device_id = state["device"]

        try:
            logger.info(f"[{task_id}] 🧠 Reference decision-execution agent analyzing...")

            # 转换当前截图为base64
            current_image_base64 = convert_screenshot_to_base64(current_screenshot_path, task_id)

            # 构建包含参考任务信息的prompt
            messages = self._build_reference_messages(state, current_image_base64)

            start_time = time.time()

            # 使用langchain的chat model调用
            model_response_obj = self.model.invoke(messages)
            model_response = model_response_obj.content
            end_time = time.time()

            logger.info(f"[{task_id}] 🧠 Reference model response time: {end_time - start_time:.2f}s")
            logger.info(f"[{task_id}] 🧠 Reference model response: \n{model_response}")

            # 解析JSON响应
            parsed_fields, action_command = self._parse_json_response(model_response, task_id)

            # 记录决策日志
            self._log_decision_immediately(parsed_fields, action_command, task_id)

            # 执行动作
            execution_result = self._execute_action_command(action_command, device_id, task_id)

            # 记录执行结果到状态历史
            self._record_execution_result(state, parsed_fields, execution_result)

            return execution_result

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Reference decision-execution agent failed: {str(e)}")
            return {
                "status": "error",
                "message": f"Agent failed: {str(e)}",
                "action": ""
            }

    def _build_reference_messages(
            self,
            state: ReferenceTaskState,
            current_image_base64: str
    ) -> list:
        """
        构建包含参考任务信息的消息列表，包含参考截图

        Args:
            state: 参考任务状态
            current_image_base64: 当前截图base64

        Returns:
            消息列表
        """
        # 构建系统prompt，包含参考任务信息
        system_prompt = build_reference_decision_prompt(state)
        print(system_prompt)

        # 获取参考任务的截图
        reference_task_id = state.get("reference_task_id", "")
        reference_images = self._get_reference_task_images(reference_task_id) if reference_task_id else []

        messages = [{
            "role": "system",
            "content": system_prompt
        }]

        # 构建用户消息内容
        user_content = []

        # 添加参考截图（如果有的话）
        if reference_images:
            user_content.append({
                "type": "text",
                "text": f"以下是参考任务的成功执行截图（共{len(reference_images)}张），请学习其界面模式和操作策略："
            })

            for i, ref_image in enumerate(reference_images[:5]):  # 最多显示5张参考截图
                user_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{ref_image}"
                    }
                })

        # 添加当前截图和分析要求
        user_content.extend([
            {
                "type": "text",
                "text": "以下是当前界面截图，请对比参考截图，学习相似场景的处理方式，决策下一步执行动作："
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{current_image_base64}"
                }
            }
        ])

        messages.append({
            "role": "user",
            "content": user_content
        })

        return messages

    def _parse_json_response(self, model_response: str, task_id: str) -> Tuple[Dict[str, Any], str]:
        """
        解析模型的JSON响应，提取各个字段
        使用OutputFixingParser来纠正格式错误的JSON

        Args:
            model_response: 模型的完整响应
            task_id: 任务ID

        Returns:
            Tuple[parsed_fields, action_command]: 解析的字段字典、动作命令

        Raises:
            Exception: 当JSON解析和修复都失败时抛出异常
        """
        try:
            # 首先尝试直接解析JSON
            json_data = json.loads(model_response.strip())
            logger.info(f"[{task_id}] ✅ Direct JSON parsing successful")

        except json.JSONDecodeError as e:
            logger.warning(f"[{task_id}] ⚠️ Direct JSON parsing failed: {str(e)}")
            logger.info(f"[{task_id}] 🔧 Attempting to fix JSON format using OutputFixingParser...")

            try:
                # 使用OutputFixingParser来修复JSON格式
                parsed_response: ReferenceDecisionResponse = self.output_parser.parse(model_response)
                json_data = parsed_response.model_dump()
                logger.info(f"[{task_id}] ✅ JSON format fixed successfully using OutputFixingParser")

            except Exception as fix_error:
                logger.error(f"[{task_id}] ❌ OutputFixingParser failed: {str(fix_error)}")
                logger.error(f"[{task_id}] Raw response: {model_response}")

                # 使用统一的异常处理方法
                TaskExceptionHandler.update_task_status_to_failed(
                    task_id,
                    f"JSON parsing and fixing failed in reference decision agent: {str(fix_error)}"
                )

                # 重新抛出异常，让外层异常处理机制处理
                raise fix_error

        # 构建返回的字段字典
        parsed_fields = {
            "self_check": json_data.get("self_check", ""),
            "interface_analysis": json_data.get("interface_analysis", ""),
            "reference_analysis": json_data.get("reference_analysis", ""),
            "current_step_name": json_data.get("current_step_name", ""),
            "action_decision": json_data.get("action_decision", ""),
            "instruction": json_data.get("instruction", ""),
            "action": json_data.get("action", "")
        }

        action_command = json_data.get("action", "")

        return parsed_fields, action_command

    def _get_reference_task_images(self, reference_task_id: str) -> list:
        """
        获取参考任务的关键截图

        Args:
            reference_task_id: 参考任务ID

        Returns:
            截图base64列表
        """
        try:
            # 获取参考任务的动作记录
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            actions = task_persistence_service.get_task_actions(reference_task_id)

            if not actions:
                return []

            images = []
            for action in actions:
                if hasattr(action, 'image_path') and action.image_path:
                    try:
                        # 转换截图为base64
                        img_base64 = convert_screenshot_to_base64(action.image_path, reference_task_id)
                        if img_base64:
                            images.append(img_base64)
                    except Exception as e:
                        logger.warning(f"Failed to load reference image {action.image_path}: {str(e)}")
                        continue

            logger.info(f"Loaded {len(images)} reference images for task {reference_task_id}")
            return images

        except Exception as e:
            logger.error(f"Error getting reference task images: {str(e)}")
            return []

    def _execute_action_command(self, action_command: str, device_id: str, task_id: str) -> Dict[str, Any]:
        """
        执行动作命令，参考execution_agent的实现方式

        Args:
            action_command: 动作命令
            device_id: 设备ID
            task_id: 任务ID

        Returns:
            执行结果
        """
        try:
            logger.info(f"[{task_id}] 🎯 Executing action command: {action_command}")

            # 处理特殊动作（finished, failed）
            if action_command.strip() == "finished()" or action_command.strip().startswith("finished("):
                return {
                    "status": "success",
                    "action": "finished",
                    "message": "Task completed successfully"
                }

            if action_command.strip().startswith("failed("):
                # 提取失败原因
                reason = "Unknown error"
                if "content=" in action_command:
                    content_match = re.search(r"content='([^']*)'", action_command)
                    if content_match:
                        reason = content_match.group(1)

                return {
                    "status": "failed",
                    "action": "failed",
                    "message": f"Task failed: {reason}"
                }

            # 处理wait动作
            if action_command.strip().startswith("wait("):
                seconds_match = re.search(r"wait\(seconds=(\d+)\)", action_command)
                if seconds_match:
                    seconds = int(seconds_match.group(1))
                else:
                    seconds = 3  # 默认等待3秒

                time.sleep(seconds)
                return {
                    "status": "success",
                    "action": f"wait({seconds})",
                    "message": f"Waited for {seconds} seconds"
                }

            # 直接使用action_tool执行动作（与execution_agent保持一致）
            logger.info(f"[{task_id}] ⚡ Executing action with action_tool: {action_command}")
            result = execute_simple_action(action_command, device_id)

            logger.info(f"[{task_id}] 🎯 Action execution result: {result}")

            return result

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error executing action command: {str(e)}")
            return {
                "status": "error",
                "message": f"Execution error: {str(e)}",
                "action": action_command
            }

    def _log_decision_immediately(self, parsed_fields: Dict[str, Any], action_command: str, task_id: str):
        """立即记录决策日志"""
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService

            step_name = parsed_fields.get("current_step_name", "")
            action_decision = parsed_fields.get("action_decision", "")

            decision_log = ExecutionLogService.create_decision_log(step_name, action_decision, action_command)
            task_persistence_service.append_execution_log_entries(task_id, [decision_log])
            logger.info(f"[{task_id}] 📝 Reference decision logged immediately")

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log reference decision: {str(e)}")

    def _record_execution_result(
            self,
            state: ReferenceTaskState,
            parsed_fields: Dict[str, Any],
            execution_result: Dict[str, Any]
    ):
        """
        记录执行结果到状态历史

        Args:
            state: 参考任务状态
            parsed_fields: 解析的字段
            execution_result: 执行结果
        """
        try:
            history_entry = {
                "execution_count": state.get("execution_count", 0) + 1,
                "parsed_fields": parsed_fields,
                "execution_result": execution_result,
                "timestamp": datetime.now().isoformat()
            }

            if "history" not in state:
                state["history"] = []

            state["history"].append(history_entry)

            # 只保留最近10条历史记录
            if len(state["history"]) > 10:
                state["history"] = state["history"][-10:]

        except Exception as e:
            logger.error(f"Error recording execution result: {str(e)}")

#!/usr/bin/env python3
"""
参考任务决策Prompt

基于参考成功案例的UI自动化测试决策prompt
参考decision_definition_prompt.py的编写风格和描述方式
"""

import json
import re
from src.domain.reference_task.repo.reference_state import ReferenceTaskState


def build_reference_decision_prompt(state: ReferenceTaskState) -> str:
    """
    构建参考任务决策prompt

    Args:
        state: 参考任务状态

    Returns:
        完整的系统prompt
    """

    # 构建参考任务信息和当前执行历史
    reference_actions_text = _build_reference_actions_text(state["reference_actions"])
    current_execution_history = _build_current_execution_history(state.get("history", []))

    prompt = f"""
########## 角色定位 ##########
{get_reference_role_definition()}

########## 测试用例信息 ##########
{get_reference_test_case_description(state)}

########## 成功案例 ##########
{reference_actions_text}

########## 当前执行历史 ##########
{current_execution_history}

########## 动作列表 ##########
{get_reference_action_list()}

########## 执行策略 ##########
{get_reference_learning_strategy()}

########## 异常场景处理 ##########
{get_reference_exception_handling()}

########## 动作决策 ##########
{get_reference_action_decision_prompt()}

########## 输出格式 ##########
{get_reference_output_example()}

########## 输出要求 ##########
{get_reference_output_requirement()}
"""

    return prompt


def get_reference_role_definition() -> str:
    """获取参考任务的角色定义"""
    return """
你是一个专业的安卓软件UI自动化测试Agent，请你按照当前用例上次执行的<成功案例>，进行回归测试

**核心职责**：
阅读<测试用例信息>和<当前执行历史>，仔细分析当前界面内容和<当前执行历史>确定当前执行步骤，再从<成功案例>中找出当前执行步骤对应的内容，从而确定下一个执行动作
"""


def get_reference_test_case_description(state: ReferenceTaskState) -> str:
    """获取参考任务的测试用例描述"""
    # 优先使用实际的测试用例名称，避免显示参考任务ID
    test_case_name = state.get("test_case_name", "未知测试用例")
    test_case_description = state.get("test_case_description", state.get("task", ""))
    expected_result = state.get("expected_result", "")
    content = f"""- **用例名称**: {test_case_name}
- **用例步骤**:
{test_case_description}
- **期望结果**: {expected_result}"""
    return content


def get_reference_action_list() -> str:
    """获取参考任务的动作列表"""
    return """
点击屏幕: click(point='<point>x1 y1</point>')
长按屏幕: long_press(point='<point>x1 y1</point>')
输入内容: type(content='text_to_input')
滑动屏幕: scroll(point='<point>x1 y1</point>', direction='down or up or right or left')
拖动元素: drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>')
等待动作: wait(seconds=wait_seconds)
删除内容: delete(content=delete_count)
返回动作: back()
失败动作: failed(content='reason')
成功动作: finished(content='success_message')"""


def get_reference_learning_strategy() -> str:
    """获取参考学习策略"""
    return """
1. 深度分析<成功案例>中的成功执行模式：
   - 仔细学习每个成功步骤的操作方式和决策思路
   - 理解参考任务在相似界面下的操作序列和策略
   - 掌握参考任务处理异常情况的方法和经验

2. 结合参考截图进行视觉学习：
   - 仔细对比参考截图和当前截图的界面布局
   - 识别相似的UI元素、按钮位置、文本内容和界面状态
   - 找到界面的相似度和差异点，确定可复用的操作模式

3. 智能策略复用：
   - 界面高度相似：直接复用<成功案例>中的坐标和操作
   - 界面部分相似：调整坐标位置，保持操作逻辑和决策思路
   - 界面差异较大：学习操作思路和处理策略，重新定位元素

4. 参考经验应用优先级：
   - 优先参考界面最相似的截图和对应操作
   - 其次参考处理相同UI组件类型的方式
   - 最后参考整体的执行策略、节奏和异常处理经验

5. 结合<当前执行历史>进行决策优化：
   - 分析当前任务已执行的步骤和结果
   - 避免重复之前失败的操作方式
   - 基于当前执行状态选择最合适的参考经验"""


def get_reference_exception_handling() -> str:
    """获取异常场景处理策略"""
    return """
* 如界面出现弹窗，且弹窗带倒计时，则调用wait(seconds=倒计时秒数)等待倒计时结束自动消失；
* 如界面出现弹窗，且弹窗不带倒计时，但附近存在'我知道了'、'同意'、'取消' 'X'等按钮，则调用click点击按钮关闭弹窗；
* 如界面边缘存在悬浮气泡遮挡了目标元素位置，可以先通过拖动(drag)上/下移动悬浮气泡，露出目标元素；
* 如界面出现未加载完成、空白页面、页面切换中、异常页面，则调用wait(seconds=3)等待加载完成，其中若'空白页'和'异常页'出现连续多次(>2次)等待则考虑系统问题；
* 如以上条件都不满足，则调用back返回上一级；"""


def get_reference_action_decision_prompt() -> str:
    """获取动作决策提示"""
    return """1. 通过分析当前界面截图，仅以本轮截图中"可见且可操作"的元素为依据，提取"当前用例步骤"需操作的目标相关元素，并保存到你的记忆中。
2. 深度学习<成功案例>中的成功经验：
   - 分析参考任务在相似界面下的操作方式和决策思路
   - 学习参考任务处理相同类型元素的成功策略
   - 理解参考任务的操作节奏和异常处理方法
3. 结合<当前执行历史>，在确保动作落地基于"本轮可见元素"的前提下，结合"记忆中的相关元素"和<测试用例信息>的"用例步骤"描述，确定下一个操作动作和目标元素：
   - 首先严格依据"用例步骤"中的页面方位/容器/序号进行定位，在该方位内确保目标唯一
   - 一定要清晰识别'用例步骤'描述的**元素特征(文字内容、形状)**和**位置信息**
   - 当当前界面出现同名或相似元素时，结合<当前执行历史>和<成功案例>的经验进行消歧
   - 保持与既往决策的连贯性：对齐已知页面/模式/列表索引与已操作记录，避免重复或自相矛盾
4. 优先考虑<异常场景处理>，按照场景说明进行操作；
5. 必须优先参考<测试用例信息>的'用例步骤'描述的动作和元素特征，每一个步骤中可能存在多个执行动作；
6. 结合<动作列表>选择指定动作，参考<成功案例>中的成功操作方式；
7. 整个测试用例步骤执行完毕，达成<测试用例信息>期望结果，调用finished，如果与期望结果不一致，则调用failed；"""


def get_reference_output_example() -> str:
    """获取输出格式要求"""
    return """{{
"interface_analysis": "当前界面分析，结合<成功案例>中相似界面的对比(保持语言简洁)",
"reference_analysis": "分析<成功案例>中相似场景的处理方式，说明可以借鉴的执行策略和操作经验(保持语言简洁)",
"current_step_name": "当前正在执行的步骤名称，从<测试用例信息>获取，按照步骤顺序一个个执行",
"action_decision": "输出<动作决策>的过程，结合<成功案例>的成功经验，每轮只能执行一个动作(保持语言简洁)",
"instruction": "根据'动作决策'的结果仅复述需要操作的动作及UI元素，禁止描述任何'用例步骤'目的、期望结果或判断条件(保持语言简洁)；
- 按照以下规范进行(保持语言简洁)：
    1. 仅可以描述一个动作，不能存在多个动作的描述，该动作和action中给出的动作是一致的
    2. 必须描述被操作的具体元素特征(文字、图形等)
    3. 描述元素绝对位置, 如：在页面的哪个区域
    4. 描述元素相对位置, 如：在哪个元素的哪个位置 (可选项)",
"action": "动作+参数，必须参照<动作列表>，action字段必须严格按照<动作列表>中的格式，确保所有引号都正确闭合，避免JSON解析错误"
}}
"""


def get_reference_output_requirement() -> str:
    """获取输出要求"""
    return """
1.使用JSON格式输出内容，严格遵循<输出格式>，保证输出内容与格式完全一致
2.参考任务执行时，需要在reference_analysis中体现对<成功案例>的应用
"""


def _build_current_execution_history(history: list) -> str:
    """
    构建当前任务执行历史，兼容多种数据格式

    Args:
        history: 当前任务执行历史列表

    Returns:
        格式化的执行历史文本
    """
    if not history:
        return "当前任务刚开始，暂无执行历史"

    history_content = "以下是当前任务的执行历史：\n"

    for i, record in enumerate(history):
        # 获取执行轮次
        record_execution_count = record.get("execution_count", i + 1)

        # 获取状态和失败信息
        record_status = record.get("status", "")

        # 显示执行记录
        if record_status != "blocked":
            formatted_record = _format_execution_record(record, record_execution_count)
            if formatted_record:
                history_content += formatted_record

    # 如果最终没有任何历史内容，返回默认信息
    if history_content == "以下是当前任务的执行历史：\n":
        return "当前任务刚开始，暂无可显示的执行历史"

    return history_content


def _format_execution_record(record: dict, execution_count: int) -> str:
    """
    格式化单个执行记录为指定的中文格式

    Args:
        record: 执行记录字典
        execution_count: 执行轮次

    Returns:
        格式化的执行记录文本
    """
    # 尝试从parsed_fields获取结构化数据
    parsed_fields = record.get("parsed_fields", {})

    # 如果有parsed_fields，优先使用
    if parsed_fields:
        interface_analysis = parsed_fields.get("interface_analysis", "")
        current_step_name = parsed_fields.get("current_step_name", "")
        action_decision = parsed_fields.get("action_decision", "")
        action = parsed_fields.get("action", "")
    else:
        # 从decision_content解析
        decision_content = record.get("decision_content", "")
        interface_analysis, current_step_name, action_decision, action = _parse_decision_content(decision_content)

    # 如果解析失败，尝试从record的其他字段获取信息
    if not current_step_name:
        current_step_name = record.get("step_name", "")

    if not action:
        action = record.get("action", "")

    if not action_decision:
        action_decision = "执行决策信息待补充"

    # 如果连步骤名称和动作都没有，则跳过这条记录
    if not current_step_name and not action:
        return ""

    # 构建格式化文本
    formatted_text = f"""
**第{execution_count}轮执行**
- 步骤名称: {current_step_name}
- 执行决策: {action_decision}
- 操作指令: {interface_analysis}
- 执行动作: {action}
"""

    return _escape_template_variables(formatted_text)


def _parse_decision_content(decision_content: str) -> tuple:
    """
    从decision_content解析出四个字段

    Args:
        decision_content: 决策内容字符串

    Returns:
        (界面分析, 步骤名称, 执行决策, 执行动作) 的元组
    """
    if not decision_content:
        return "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    action = ""

    # 尝试解析JSON格式
    try:
        if decision_content.strip().startswith('{'):
            data = json.loads(decision_content)
            interface_analysis = data.get("interface_analysis", "")
            current_step_name = data.get("current_step_name", "")
            action_decision = data.get("action_decision", "")
            action = data.get("action", "")
            return interface_analysis, current_step_name, action_decision, action
    except:
        pass

    # 使用正则表达式解析
    lines = decision_content.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'.*界面分析.*[:：]', line):
            interface_analysis = re.sub(r'.*界面分析.*[:：]\s*', '', line)
        elif re.match(r'.*步骤名称.*[:：]', line):
            current_step_name = re.sub(r'.*步骤名称.*[:：]\s*', '', line)
        elif re.match(r'.*执行决策.*[:：]', line):
            action_decision = re.sub(r'.*执行决策.*[:：]\s*', '', line)
        elif re.match(r'.*执行动作.*[:：]', line):
            action = re.sub(r'.*执行动作.*[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, action


def _escape_template_variables(text: str) -> str:
    """
    转义文本中的模板变量符号，防止LangChain将其识别为变量

    Args:
        text: 原始文本

    Returns:
        转义后的文本
    """
    if not text:
        return text

    # 将单个花括号转义为双花括号
    # 这样LangChain就不会将其识别为模板变量
    return text.replace("{", "{{").replace("}", "}}")


def _build_reference_actions_text(reference_actions: list) -> str:
    """
    构建参考任务动作列表的文本描述

    Args:
        reference_actions: 参考动作列表

    Returns:
        格式化的动作描述文本
    """
    if not reference_actions:
        return "暂无参考任务历史"

    actions_text = "以下是参考任务的执行历史，供你学习和参考：\n\n"

    for i, action in enumerate(reference_actions, 1):
        formatted_action = _format_execution_record(action, i)
        if formatted_action:
            actions_text += formatted_action

    return actions_text

